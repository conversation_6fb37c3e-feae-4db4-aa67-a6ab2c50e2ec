#!/usr/bin/env python3
"""
Startup script for Dealer AI
Provides easy commands to run different parts of the system
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def check_environment():
    """Check if environment is properly set up"""
    print("🔍 Checking environment...")
    
    # Check if .env file exists
    if not Path(".env").exists():
        print("❌ .env file not found. Please run 'python install.py' first")
        return False
    
    # Check if required packages are installed
    try:
        import langchain
        import playwright
        import twilio
        import fastapi
        print("✅ All required packages are installed")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Please run 'python install.py' to install dependencies")
        return False
    
    # Check environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = ["OPENROUTER_API_KEY", "TWILIO_SID", "TWILIO_AUTH_TOKEN"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please update your .env file with the required credentials")
        return False
    
    print("✅ Environment is properly configured")
    return True

def run_server(host="127.0.0.1", port=8000, reload=True):
    """Start the FastAPI server"""
    print(f"🚀 Starting Dealer AI server on {host}:{port}")
    
    if not check_environment():
        return
    
    cmd = [
        "uvicorn", 
        "core.endpoint:app",
        "--host", host,
        "--port", str(port)
    ]
    
    if reload:
        cmd.append("--reload")
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start server: {e}")
    except KeyboardInterrupt:
        print("\n👋 Server stopped")

def run_tests():
    """Run the test suite"""
    print("🧪 Running Dealer AI tests...")
    
    if not check_environment():
        return
    
    try:
        subprocess.run([sys.executable, "test_agent.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Tests failed: {e}")
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted")

def install_dependencies():
    """Install dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.run([sys.executable, "install.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")

def test_message(message, phone="+255000000000"):
    """Test a specific message"""
    print(f"💬 Testing message: '{message}'")
    
    if not check_environment():
        return
    
    import asyncio
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    from core.agent import process_whatsapp_message
    
    async def test():
        try:
            result = await process_whatsapp_message(message, phone)
            
            print("\n📊 Result:")
            print(f"Success: {result.get('success', False)}")
            
            if result.get('success'):
                print(f"Response: {result.get('response', 'No response')}")
            else:
                print(f"Error: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test())

def show_status():
    """Show system status"""
    print("📊 Dealer AI System Status")
    print("=" * 40)
    
    # Check environment
    env_ok = check_environment()
    print(f"Environment: {'✅ OK' if env_ok else '❌ Issues'}")
    
    # Check if server is running
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"Server: ✅ Running ({data.get('status', 'unknown')})")
            print(f"Agent: ✅ {data.get('agent', 'unknown')}")
            print(f"Tools: {data.get('tools', 'unknown')}")
        else:
            print("Server: ❌ Not responding properly")
    except:
        print("Server: ❌ Not running")
    
    # Check store directory
    store_path = Path("store")
    if store_path.exists():
        products_file = store_path / "products.json"
        if products_file.exists():
            print(f"Products: ✅ File exists ({products_file.stat().st_size} bytes)")
        else:
            print("Products: ⚠️  No products file")
    else:
        print("Store: ❌ Directory missing")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Dealer AI Control Script")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Server command
    server_parser = subparsers.add_parser("server", help="Start the server")
    server_parser.add_argument("--host", default="127.0.0.1", help="Host to bind to")
    server_parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    server_parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    
    # Test command
    test_parser = subparsers.add_parser("test", help="Run tests")
    
    # Install command
    install_parser = subparsers.add_parser("install", help="Install dependencies")
    
    # Message test command
    message_parser = subparsers.add_parser("message", help="Test a specific message")
    message_parser.add_argument("text", help="Message text to test")
    message_parser.add_argument("--phone", default="+255000000000", help="Phone number")
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Show system status")
    
    args = parser.parse_args()
    
    if args.command == "server":
        run_server(args.host, args.port, not args.no_reload)
    elif args.command == "test":
        run_tests()
    elif args.command == "install":
        install_dependencies()
    elif args.command == "message":
        test_message(args.text, args.phone)
    elif args.command == "status":
        show_status()
    else:
        print("🤖 Dealer AI Control Script")
        print("\nAvailable commands:")
        print("  server   - Start the FastAPI server")
        print("  test     - Run the test suite")
        print("  install  - Install dependencies")
        print("  message  - Test a specific message")
        print("  status   - Show system status")
        print("\nExamples:")
        print("  python start.py server")
        print("  python start.py test")
        print("  python start.py message 'Samsung phone'")
        print("  python start.py status")

if __name__ == "__main__":
    main()
