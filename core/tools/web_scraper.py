"""
Web Scraping Tool for Dealer AI
Uses Playwright for browser automation and GPT-3.5 for dynamic element detection
"""

import json
import os
import asyncio
from typing import Dict, Any, List, Optional, Type
from pydantic import BaseModel, Field

from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from playwright.async_api import async_playwright, <PERSON>, Browser
from dotenv import load_dotenv

from ..site_profiles import get_site_profile, SiteType, site_manager
from ..utils.retry import retry_with_backoff, RetryError

load_dotenv()

class WebScrapingInput(BaseModel):
    """Input schema for web scraping"""
    intent_data: str = Field(description="JSON string containing extracted intent data")
    site_preference: Optional[str] = Field(default="jiji", description="Preferred site to scrape")

class WebScrapingTool(BaseTool):
    """
    Tool that performs web scraping using Playwright with GPT-3.5 assistance
    for dynamic element detection and intelligent scraping
    """
    
    name: str = "web_scraper"
    description: str = """
    Scrape e-commerce sites for product information. Use this tool when you need to search
    for products on websites like jiji.co.tz. Provide the extracted intent data as JSON.
    
    The tool will:
    1. Open the specified e-commerce site
    2. Use GPT-3.5 to find search elements dynamically
    3. Perform the search with extracted product information
    4. Apply filters if specified (price, condition)
    5. Scrape product data from results
    6. Return structured product information
    """
    args_schema: Type[BaseModel] = WebScrapingInput
    
    @property
    def max_products(self):
        return int(os.getenv("MAX_PRODUCTS", "20"))

    @property
    def scroll_attempts(self):
        return int(os.getenv("SCROLL_ATTEMPTS", "3"))

    @property
    def retry_attempts(self):
        return int(os.getenv("RETRY_ATTEMPTS", "3"))

    def _get_llm(self):
        """Get LLM instance"""
        return ChatOpenAI(
            model="openai/gpt-4o-mini",
            openai_api_key=os.getenv("OPENROUTER_API_KEY"),
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=1500
        )
    
    def _run(self, intent_data: str, site_preference: str = "jiji") -> str:
        """Synchronous web scraping (not recommended for this use case)"""
        return asyncio.run(self._arun(intent_data, site_preference))
    
    async def _arun(self, intent_data: str, site_preference: str = "jiji") -> str:
        """Perform web scraping asynchronously"""
        try:
            # Parse intent data
            intent = json.loads(intent_data)
            
            if not intent.get("is_product_request", False):
                return json.dumps({
                    "success": False,
                    "error": "Not a product request",
                    "products": []
                })
            
            # Get site profile
            site_profile = get_site_profile(site_preference)
            if not site_profile:
                return json.dumps({
                    "success": False,
                    "error": f"Site {site_preference} not supported",
                    "products": []
                })
            
            # Perform scraping
            products = await self._scrape_site(intent, site_profile)
            
            return json.dumps({
                "success": True,
                "site": site_preference,
                "query": intent.get("product_name", ""),
                "products": products,
                "total_found": len(products)
            }, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"Scraping failed: {str(e)}",
                "products": []
            })
    
    @retry_with_backoff(max_attempts=3, base_delay=2.0, exceptions=(Exception,))
    async def _scrape_site(self, intent: Dict[str, Any], site_profile) -> List[Dict[str, Any]]:
        """Main scraping logic with retry support"""
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(
                headless=True,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )

            try:
                page = await browser.new_page()
                await page.set_viewport_size({"width": 1920, "height": 1080})

                # Navigate to site with retry
                await self._navigate_with_retry(page, site_profile.base_url)

                # Perform search
                search_success = await self._perform_search(page, intent, site_profile)
                if not search_success:
                    return []

                # Apply filters if needed
                await self._apply_filters(page, intent, site_profile)

                # Scrape products
                products = await self._scrape_products(page, site_profile)

                return products[:self.max_products]

            finally:
                await browser.close()

    async def _navigate_with_retry(self, page: Page, url: str, max_attempts: int = 3):
        """Navigate to URL with retry logic"""
        for attempt in range(max_attempts):
            try:
                await page.goto(url, wait_until="networkidle", timeout=30000)
                return
            except Exception as e:
                if attempt == max_attempts - 1:
                    raise e
                print(f"Navigation attempt {attempt + 1} failed: {e}")
                await asyncio.sleep(2 ** attempt)
    
    async def _perform_search(self, page: Page, intent: Dict[str, Any], site_profile) -> bool:
        """Perform search on the site"""
        try:
            product_name = intent.get("product_name", "")
            if not product_name:
                return False
            
            # Try to find search bar using profile selectors
            search_bar = None
            for selector in [site_profile.selectors["search_bar"]]:
                try:
                    search_bar = page.locator(selector).first
                    if await search_bar.is_visible(timeout=5000):
                        break
                except:
                    continue
            
            # If profile selectors don't work, use GPT to find search elements
            if not search_bar or not await search_bar.is_visible():
                search_bar = await self._find_search_elements_with_gpt(page)
            
            if not search_bar:
                return False
            
            # Perform the search
            await search_bar.fill(product_name)
            await search_bar.press("Enter")
            
            # Wait for results
            await page.wait_for_timeout(site_profile.wait_after_search)
            
            return True
            
        except Exception as e:
            print(f"Search failed: {e}")
            return False
    
    async def _find_search_elements_with_gpt(self, page: Page) -> Optional[Any]:
        """Use GPT-3.5 to find search elements dynamically"""
        try:
            # Get page HTML
            html_content = await page.content()
            
            # Truncate HTML to avoid token limits
            if len(html_content) > 10000:
                html_content = html_content[:10000] + "..."
            
            prompt = f"""
            You are helping to find the search input element on this e-commerce page.
            
            HTML content:
            {html_content}
            
            Find the CSS selector for the main search input field. Look for:
            - Input elements with type="text" or type="search"
            - Elements with names like "query", "search", "q"
            - Elements with placeholders containing "search"
            - Main search bars (not filters)
            
            Return ONLY the CSS selector, nothing else. Examples:
            input[name="query"]
            #search-input
            .search-bar input
            """
            
            response = await self._get_llm().ainvoke([HumanMessage(content=prompt)])
            selector = response.content.strip()
            
            # Try the GPT-suggested selector
            try:
                element = page.locator(selector).first
                if await element.is_visible(timeout=3000):
                    return element
            except:
                pass
            
            return None
            
        except Exception as e:
            print(f"GPT element finding failed: {e}")
            return None

    async def _apply_filters(self, page: Page, intent: Dict[str, Any], site_profile):
        """Apply price and condition filters if specified"""
        try:
            max_price = intent.get("max_price")
            condition = intent.get("condition")

            if max_price:
                await self._apply_price_filter(page, max_price, site_profile)

            if condition:
                await self._apply_condition_filter(page, condition, site_profile)

        except Exception as e:
            print(f"Filter application failed: {e}")

    async def _apply_price_filter(self, page: Page, max_price: str, site_profile):
        """Apply maximum price filter"""
        try:
            price_filter = site_profile.filters.get("price", {})
            max_selector = price_filter.get("max_selector")

            if max_selector:
                max_input = page.locator(max_selector).first
                if await max_input.is_visible(timeout=3000):
                    await max_input.fill(str(max_price))
                    await page.wait_for_timeout(1000)

        except Exception as e:
            print(f"Price filter failed: {e}")

    async def _apply_condition_filter(self, page: Page, condition: str, site_profile):
        """Apply condition filter (new/used)"""
        try:
            condition_filter = site_profile.filters.get("condition", {})
            selector = condition_filter.get("selector")
            options = condition_filter.get("options", {})

            if selector and condition in options:
                condition_select = page.locator(selector).first
                if await condition_select.is_visible(timeout=3000):
                    await condition_select.select_option(options[condition])
                    await page.wait_for_timeout(1000)

        except Exception as e:
            print(f"Condition filter failed: {e}")

    async def _scrape_products(self, page: Page, site_profile) -> List[Dict[str, Any]]:
        """Scrape product information from the results page"""
        products = []

        try:
            # Scroll to load more products
            for i in range(self.scroll_attempts):
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(2000)

            # Find product cards
            product_cards = await self._find_product_cards(page, site_profile)

            if not product_cards:
                return products

            # Extract data from each product card
            card_count = await product_cards.count()

            for i in range(min(card_count, self.max_products)):
                try:
                    card = product_cards.nth(i)
                    product_data = await self._extract_product_data(card, site_profile)

                    if product_data:
                        products.append(product_data)

                except Exception as e:
                    print(f"Failed to extract product {i}: {e}")
                    continue

            return products

        except Exception as e:
            print(f"Product scraping failed: {e}")
            return products

    async def _find_product_cards(self, page: Page, site_profile):
        """Find product cards on the page"""
        try:
            # Try profile selector first
            cards = page.locator(site_profile.product_card_selector)
            if await cards.count() > 0:
                return cards

            # Try fallback selectors
            fallback_selectors = site_manager.get_fallback_selectors(SiteType.JIJI)
            for selector in fallback_selectors.get("product_cards", []):
                try:
                    cards = page.locator(selector)
                    if await cards.count() > 0:
                        return cards
                except:
                    continue

            return None

        except Exception as e:
            print(f"Finding product cards failed: {e}")
            return None

    async def _extract_product_data(self, card, site_profile) -> Optional[Dict[str, Any]]:
        """Extract data from a single product card"""
        try:
            product_data = {}

            # Extract title
            title_selector = site_profile.selectors["product_title"]
            title_element = card.locator(title_selector).first
            product_data["title"] = await title_element.inner_text() if await title_element.count() > 0 else "N/A"

            # Extract price
            price_selector = site_profile.selectors["product_price"]
            price_element = card.locator(price_selector).first
            product_data["price"] = await price_element.inner_text() if await price_element.count() > 0 else "N/A"

            # Extract image
            image_selector = site_profile.selectors["product_image"]
            image_element = card.locator(image_selector).first
            if await image_element.count() > 0:
                product_data["image"] = await image_element.get_attribute("src") or await image_element.get_attribute("data-src") or "N/A"
            else:
                product_data["image"] = "N/A"

            # Extract link
            link_selector = site_profile.selectors["product_link"]
            link_element = card.locator(link_selector).first
            if await link_element.count() > 0:
                href = await link_element.get_attribute("href")
                if href and not href.startswith("http"):
                    href = site_profile.base_url + href
                product_data["link"] = href or "N/A"
            else:
                product_data["link"] = "N/A"

            # Extract location if available
            if "product_location" in site_profile.selectors:
                location_selector = site_profile.selectors["product_location"]
                location_element = card.locator(location_selector).first
                product_data["location"] = await location_element.inner_text() if await location_element.count() > 0 else "N/A"

            return product_data

        except Exception as e:
            print(f"Product data extraction failed: {e}")
            return None
